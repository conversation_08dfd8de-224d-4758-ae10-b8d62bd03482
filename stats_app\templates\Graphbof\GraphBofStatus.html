<!-- stats_app/templates/graphbof/GraphBofStatus.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Graph BOF Status</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
            font-family: Arial, sans-serif;
        }

        th, td {
            border: 1px solid #444;
            padding: 8px;
            text-align: center;
        }

        th {
            background-color: #333;
            color: white;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        h2 {
            text-align: center;
        }
    </style>
</head>
<body>
    <h2>Graph BOF Status</h2>
    <table>
        <thead>
            <tr>
                <th>Sr No</th>
                <th>Heat ID</th>
                <th>Start Date</th>
                <th>Stop Date</th>
                <th>Steel Grade</th>
                <th>Hot Metal Temp</th>
                <th>Hot Metal Weight</th>
                <th>Total Scrap</th>
                <th>Total Oxygen</th>
                <th>Tap to Tap</th>
                <th>Blowing Time</th>
                <th>Aim Carbon</th>
                <th>Actual Carbon</th>
                <th>Aim Temp</th>
                <th>Actual Temp</th>
            </tr>
        </thead>
        <tbody>
            {% for row in graph_bof_data %}
            <tr>
                <td>{{ row.Sr_no }}</td>
                <td>{{ row.Heat_id }}</td>
                <td>{{ row.Start_date }}</td>
                <td>{{ row.Stop_date }}</td>
                <td>{{ row.Steel_grade }}</td>
                <td>{{ row.Hot_metal_temperature }}</td>
                <td>{{ row.Hot_metal_weight }}</td>
                <td>{{ row.Total_scrap_charge }}</td>
                <td>{{ row.Total_oxygen_consumption }}</td>
                <td>{{ row.Tap_to_tap_time }}</td>
                <td>{{ row.Blowing_duration }}</td>
                <td>{{ row.Aim_carbon }}</td>
                <td>{{ row.Actual_carbon }}</td>
                <td>{{ row.Aim_temperature }}</td>
                <td>{{ row.Actual_temperature }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
