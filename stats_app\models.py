from django.db import models

class GraphBof(models.Model):
    sr_no = models.AutoField(primary_key=True, db_column='Sr_no')
    heat_id = models.CharField(max_length=50, db_column='Heat_id')
    start_date = models.DateTimeField(db_column='Start_date')
    stop_date = models.DateTimeField(db_column='Stop_date')
    steel_grade = models.CharField(max_length=50, db_column='Steel_grade')
    hot_metal_temperature = models.CharField(max_length=50, db_column='Hot_metal_temperature')
    hot_metal_weight = models.CharField(max_length=50, db_column='Hot_metal_weight')
    total_scrap_charge = models.CharField(max_length=50, db_column='Total_scrap_charge')
    total_oxygen_consumption = models.CharField(max_length=50, db_column='Total_oxygen_consumption')
    tap_to_tap_time = models.CharField(max_length=50, db_column='Tap_to_tap_time')
    blowing_duration = models.CharField(max_length=50, db_column='Blowing_duration')
    aim_carbon = models.CharField(max_length=50, db_column='Aim_carbon')
    actual_carbon = models.CharField(max_length=50, db_column='Actual_carbon')
    aim_temperature = models.CharField(max_length=50, db_column='Aim_temperature')
    actual_temperature = models.CharField(max_length=50, db_column='Actual_temperature')

    class Meta:
        db_table = 'GraphBof'



