from django.shortcuts import render
from django.db import connection
from rest_framework.decorators import api_view
from rest_framework.response import Response
from .models import GraphBof
from .serializers import GraphBofSerializer

# View to render HTML page using raw SQL and cursor
def graph_bof_status_view(request):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT Sr_no, Heat_id, Start_date, Stop_date, Steel_grade,
                   Hot_metal_temperature, Hot_metal_weight, Total_scrap_charge,
                   Total_oxygen_consumption, Tap_to_tap_time, Blowing_duration,
                   Aim_carbon, Actual_carbon, Aim_temperature, Actual_temperature
            FROM Graph.dbo.GraphBof
        """)
        rows = cursor.fetchall()

    graph_bof_data = [
        {
            'Sr_no': row[0],
            'Heat_id': row[1],
            'Start_date': row[2],
            'Stop_date': row[3],
            'Steel_grade': row[4],
            'Hot_metal_temperature': row[5],
            'Hot_metal_weight': row[6],
            'Total_scrap_charge': row[7],
            'Total_oxygen_consumption': row[8],
            'Tap_to_tap_time': row[9],
            'Blowing_duration': row[10],
            'Aim_carbon': row[11],
            'Actual_carbon': row[12],
            'Aim_temperature': row[13],
            'Actual_temperature': row[14],
        }
        for row in rows
    ]

    return render(request, 'graphbof/GraphBofStatus.html', {'graph_bof_data': graph_bof_data})

# API view to return data as JSON
@api_view(['GET'])
def graph_bof_api(request):
    graph_bof_objects = GraphBof.objects.all()
    serializer = GraphBofSerializer(graph_bof_objects, many=True)
    return Response(serializer.data)
